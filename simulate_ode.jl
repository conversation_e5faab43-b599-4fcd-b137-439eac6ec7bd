#=
This Julia script provides an analytical solution to a system of 2L coupled
linear first-order differential equations. It computes the solution based on
the eigenvalue decomposition of the system's matrix.

The script performs the following steps:
1.  Calculates the 2L eigenvalues and corresponding right eigenvectors.
2.  Forms a matrix U from the eigenvectors.
3.  Determines the expansion coefficients by solving the linear system U*C = v0.
4.  Constructs the full time-dependent solution v(t).
5.  Calculates the error (1 - fidelity) relative to the steady state.
6.  Takes the logarithm of the error data.
7.  Performs a linear fit to the (time, log(error)) data to find the decay rate.
8.  Plots the error on a log-scale and overlays the linear fit.
=#

using LinearAlgebra
using Plots
using LsqFit # For curve fitting
using LinearAlgebra: norm

"""
    solve_analytically(L, p, q, v0, t_span)

Solves the coupled ODE system analytically using eigenvalue decomposition.
"""
function solve_analytically(L::Int, p::Float64, q::Float64, v0::Vector{Float64}, t_span)
    @assert length(v0) == 2L "Initial condition vector v0 must have length 2L."

    # --- 1. Calculate Eigenvalues (λ) ---
    eigenvalues = ComplexF64[]
    for k in 0:(L-1)
        omega_k = exp(2im * π * k / L)
        discriminant = sqrt(complex((p - q)^2 + 4 * p * q * omega_k))
        lambda_plus = (- (p + q) + discriminant) / 2
        lambda_minus = (- (p + q) - discriminant) / 2
        push!(eigenvalues, lambda_plus, lambda_minus)
    end

    # --- 2. Calculate Right Eigenvectors (u) ---
    right_eigenvectors = Matrix{ComplexF64}(undef, 2L, 2L)
    for (i, lambda) in enumerate(eigenvalues)
        u = Vector{ComplexF64}(undef, 2L)
        if abs(lambda + p) < 1e-9 || abs(lambda + q) < 1e-9
             fill!(u, 0)
        else
            u[1] = 1.0 + 0.0im
            for j in 2:L
                u[j] = u[j-1] * p / (lambda + p)
            end
            u[L+1] = u[L] * p / (lambda + q)
            for j in (L+2):(2L)
                u[j] = u[j-1] * q / (lambda + q)
            end
        end
        right_eigenvectors[:, i] = u
    end
    
    # --- 3. Calculate Coefficients (C) by solving the linear system U*C = v0 ---
    U = right_eigenvectors
    coeffs = U \ Vector{ComplexF64}(v0)

    # --- 4. Construct the Solution v(t) ---
    solution = zeros(Float64, 2L, length(t_span))
    for (j, t) in enumerate(t_span)
        v_t = zeros(ComplexF64, 2L)
        for i in 1:2L
            v_t .+= coeffs[i] * exp(eigenvalues[i] * t) * right_eigenvectors[:, i]
        end
        solution[:, j] = real.(v_t)
    end

    return solution
end

"""
Calculates the fidelity-like overlap (Bhattacharyya coefficient) between two vectors.
"""
function calculate_fidelity(v1, v2)
    v1_safe = max.(v1, 0)
    v2_safe = max.(v2, 0)
    sum_v1 = sum(v1_safe)
    sum_v2 = sum(v2_safe)
    
    if sum_v1 < 1e-9 || sum_v2 < 1e-9
        return 0.0
    end
    
    numerator = dot(sqrt.(v1_safe), sqrt.(v2_safe))
    denominator = sqrt(sum_v1 * sum_v2)
    
    return numerator / denominator
end

# --- Main Execution ---

# --- System Parameters ---
L = 10000
p = 1.0
q = 1.0

# --- Initial Conditions ---
v0 = zeros(Float64, 2L)
v0[1:L] .= 1.0
v0[(L+1):2L] .= 0.5
v0 = v0 / sum(v0)

# --- Time Span ---
t_start = 0.0
t_end = 100
dt = t_end / 20
t_span = t_start:dt:t_end

# --- Solve Analytically ---
println("Solving analytically...")
analytical_sol = solve_analytically(L, p, q, v0, t_span)
println("Analytical solution complete.")

# --- Calculate Theoretical Steady State ---
V_total_initial = sum(v0)
v_ss_theory = zeros(Float64, 2L)
ss_val_1 = q * V_total_initial / (L * (p + q))
ss_val_2 = p * V_total_initial / (L * (p + q))
v_ss_theory[1:L] .= ss_val_1
v_ss_theory[(L+1):2L] .= ss_val_2

# --- Calculate Error (1 - Fidelity) over time ---
error_data = [1.0 - calculate_fidelity(analytical_sol[:, i], v_ss_theory) for i in 1:length(t_span)]

plot(t_span, error_data)

println("\n--- Final State Analysis ---")
println("Final Error at t=$t_end: ", error_data[end])

##

# --- [NEW] Perform Linear Fit on Logarithm of Error Data ---
println("Performing linear fit on log(error) data...")

# Define the linear model: f(t, p) = slope*t + intercept
# p[1] is the slope (m), p[2] is the intercept (c)
@. linear_model(t, p) = p[1] * t + p[2]

# Filter data to include only points where error > 0 for log
fit_indices = findall(y -> y > 1e-9, error_data)

gamma_fit = NaN
p_fit = [NaN, NaN]

if length(fit_indices) < 2
    println("Warning: Not enough positive data points to perform a fit.")
else
    t_fit = t_span[fit_indices]
    # Take the log of the error for fitting
    log_error_fit = log.(error_data[fit_indices])
    
    # Initial guess for parameters [slope, intercept]
    p0 = [-0.01, log_error_fit[1]] 
    
    fit_result = curve_fit(linear_model, t_fit, log_error_fit, p0)
    p_fit = coef(fit_result)
    
    # The decay rate gamma is the negative of the slope
    gamma_fit = -p_fit[1]
    println("Fit complete. Decay Rate γ = ", gamma_fit)
end

# --- Plotting the Results ---
# Filter data for plotting to avoid log(0) issues
plot_indices = findall(x -> x > 0, error_data)
t_plot = t_span[plot_indices]
error_plot = error_data[plot_indices]

p_distance = plot(t_plot, error_plot,
    title="Approach to Steady State (L=$L)",
    xlabel="Time (t)",
    ylabel="Error (1 - Fidelity)",
    yscale=:log10,
    label="Simulation Error",
    legend=:bottomleft,
    linewidth=2.5,
    lc=:blue
)

# Plot the fitted line. We must exponentiate the linear model's output
# to display it correctly on the log-scale plot.
if !isnan(gamma_fit)
    plot!(p_distance, t_span, exp.(linear_model(t_span, p_fit)),
        label="Linear Fit on Log Data (γ ≈ $(round(gamma_fit, sigdigits=4)))",
        linewidth=2,
        linestyle=:dash,
        lc=:red
    )
end

display(p_distance)

println("\n--- Final State Analysis ---")
println("Final Error at t=$t_end: ", error_data[end])

