# Convergence-to-steady-state demo for the 2L ODE system
# (Uses the closed-form spectral solution we derived.)
#
# It computes v(t), the stationary distribution π, the distance ||v(t)-π||,
# and compares it to the theoretical exponential envelope set by the spectral gap.
#
# You can tweak the parameters in the "User parameters" section below.

import numpy as np
import matplotlib.pyplot as plt

# ---------- Closed-form machinery (copied in full for a self-contained cell) ----------

def build_generator(L: int, p: float, q: float) -> np.ndarray:
    n = 2 * L
    M = np.zeros((n, n), dtype=float)
    # first arc (indices 0..L-1)
    M[0, 0] = -p
    for j in range(1, L):
        M[j, j] = -p
        M[j, j - 1] = p
    # second arc (indices L..2L-1)
    M[L, L] = -q
    M[L, L - 1] = p
    for j in range(L + 1, 2 * L):
        M[j, j] = -q
        M[j, j - 1] = q
    # seam
    M[0, 2 * L - 1] = q  # v_{2L} -> v_1
    return M

def eigenvalues_closed_form(L: int, p: float, q: float):
    m = np.arange(L)
    omega = np.exp(2j * np.pi * m / L)
    S = p + q
    D = (p - q) ** 2 + 4 * p * q * omega
    sqrtD = np.sqrt(D)  # principal branch
    lam_plus = -S / 2 + 0.5 * sqrtD
    lam_minus = -S / 2 - 0.5 * sqrtD
    return lam_plus, lam_minus

def right_eigenvector_closed_form(L: int, p: float, q: float, lam: complex) -> np.ndarray:
    n = 2 * L
    r = np.zeros(n, dtype=complex)
    rho_p = p / (lam + p)
    rho_q = q / (lam + q)
    # first arc (1..L in 1-based -> 0..L-1)
    r[:L] = rho_p ** np.arange(L, dtype=complex)
    # seam value at L+1 (index L in 0-based)
    r[L] = (p / (lam + q)) * rho_p ** (L - 1)
    # second arc (L+2..2L -> L+1..2L-1)
    if L + 1 < 2 * L:
        r[L + 1:] = r[L] * (rho_q ** np.arange(1, L, dtype=complex))
    return r

def assemble_right_eigenbasis(L: int, p: float, q: float):
    lam_p, lam_m = eigenvalues_closed_form(L, p, q)
    lambdas = np.empty(2 * L, dtype=complex)
    R = np.empty((2 * L, 2 * L), dtype=complex)
    idx = 0
    for m in range(L):
        for lam in (lam_p[m], lam_m[m]):
            lambdas[idx] = lam
            R[:, idx] = right_eigenvector_closed_form(L, p, q, lam)
            idx += 1
    return lambdas, R

def closed_form_solution(L: int, p: float, q: float, v0: np.ndarray, t: np.ndarray) -> np.ndarray:
    v0 = np.asarray(v0, dtype=complex)
    assert v0.shape == (2 * L,)
    lambdas, R = assemble_right_eigenbasis(L, p, q)
    c = np.linalg.solve(R, v0)
    vt = np.empty((len(t), 2 * L), dtype=complex)
    for i, ti in enumerate(t):
        expfac = np.exp(lambdas * ti)
        vt[i] = R @ (expfac * c)
    return vt

def stationary_distribution(L: int, p: float, q: float) -> np.ndarray:
    # Unique (up to scale) right eigenvector with λ=0:
    # constant on each arc with ratio p/q between arcs; normalize to sum 1.
    pi = np.zeros(2 * L, dtype=float)
    pi[:L] = q  # first arc weight ∝ q
    pi[L:] = p  # second arc weight ∝ p
    pi = pi / (L * (p + q))  # normalize: sum = 1
    return pi

# ---------- User parameters ----------

L = 80
p = 1.3
q = 0.7

n = 2 * L

# Initial distribution: put all mass at site 1 (Dirac), or use random
v0 = np.zeros(n, dtype=float)
v0[0] = 1.0
# Alternative random initialization (comment/uncomment as desired)
# rng = np.random.default_rng(0)
# v0 = rng.random(n)
# v0 = v0 / v0.sum()

tmax = L**2  # total time span
nt = 400      # number of time points
t = np.linspace(0.0, tmax, nt)

# ---------- Compute solution, distances, and theoretical envelope ----------

# Closed-form solution
vt = closed_form_solution(L, p, q, v0, t).real  # real valued for probability vectors

# Stationary distribution
pi = stationary_distribution(L, p, q)

# Distances to stationarity
def tv_distance(v, pi):
    # total variation distance = 0.5 * L1
    return 0.5 * np.sum(np.abs(v - pi))

def l2_distance(v, pi):
    return np.linalg.norm(v - pi)

TV = np.array([tv_distance(vt[i], pi) for i in range(nt)])
L2 = np.array([l2_distance(vt[i], pi) for i in range(nt)])

# Theoretical spectral gap (slowest non-zero decay)
lam_p, lam_m = eigenvalues_closed_form(L, p, q)
# The eigenvalue closest to zero is lam_p[1] (m=1, '+' branch)
lam_gap = lam_p[1]
gap_rate = -lam_gap.real  # > 0
# Build exponential envelope using initial L2 distance
envelope = L2[0] * np.exp(-gap_rate * t)

# Optional: estimate observed late-time slope by linear fit of log L2
fit_start = int(0.6 * nt)
coeffs = np.polyfit(t[fit_start:], np.log(L2[fit_start:] + 1e-300), 1)
observed_rate = -coeffs[0]

# ---------- Plots ----------

# Plot L2 distance and exponential envelope
plt.figure(figsize=(7,4))
plt.semilogy(t, L2, label="||v(t)-π||₂")
plt.semilogy(t, envelope, linestyle="--", label=f"envelope ~ exp(-Δ t), Δ≈{gap_rate:.4e}")
plt.xlabel("t")
plt.ylabel("distance to steady state")
plt.title("Convergence to steady state: L2 distance and gap envelope")
plt.legend()
plt.show()

# Plot selected components vs. stationary values (few sample sites)
plt.figure(figsize=(7,4))
sample_sites = [0, L-1, L, 2*L-1]  # endpoints of arcs
for j in sample_sites:
    plt.plot(t, vt[:, j], label=f"v[{j+1}]")
    plt.hlines(pi[j], t[0], t[-1], linestyles="dotted")
plt.xlabel("t")
plt.ylabel("v_j(t)")
plt.title("Selected components vs. their stationary values (dotted)")
plt.legend()
plt.show()

# Print summary
print(f"Spectral gap estimate (theory): Δ ≈ {gap_rate:.6e}")
print(f"Observed late-time decay rate from fit: ≈ {observed_rate:.6e}")
print(f"Initial TV distance: {TV[0]:.6f}  ->  TV at final time: {TV[-1]:.6e}")
