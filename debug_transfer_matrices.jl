using LinearAlgebra
using SparseArrays
using Printf

# Include both implementations
include("exact_diag_toom.jl")
include("toom_transfer_matrix.jl")

function compare_transfer_matrices()
    # Same parameters
    Lx, Ly = 3, 2
    p = 0.00005
    q = 0.00001
    
    println("Creating transfer matrices with identical parameters...")
    println("Lx=$Lx, Ly=$Ly, p=$p, q=$q")
    
    # Create matrices from both files
    T1 = Main.create_toom_transfer_matrix_test(Lx, Ly, p, q)  # from exact_diag_toom.jl
    T2 = create_toom_transfer_matrix(Lx, Ly, p, q)       # from toom_transfer_matrix.jl
    
    println("\nMatrix sizes:")
    println("T1 (exact_diag): $(size(T1))")
    println("T2 (toom_transfer): $(size(T2))")
    
    println("\nNon-zero elements:")
    println("T1: $(nnz(T1))")
    println("T2: $(nnz(T2))")
    
    # Check if matrices are identical
    if size(T1) == size(T2)
        diff_matrix = T1 - T2
        max_diff = maximum(abs.(diff_matrix))
        println("\nMaximum difference between matrices: $max_diff")
        
        if max_diff > 1e-12
            println("Matrices are DIFFERENT!")
            
            # Find the largest differences
            I, J, V = findnz(diff_matrix)
            sorted_indices = sortperm(abs.(V), rev=true)
            
            println("\nTop 10 largest differences:")
            for i in 1:min(10, length(V))
                idx = sorted_indices[i]
                row, col, val = I[idx], J[idx], V[idx]
                println("T1[$row,$col] - T2[$row,$col] = $val")
                println("  T1[$row,$col] = $(T1[row,col])")
                println("  T2[$row,$col] = $(T2[row,col])")
            end
        else
            println("Matrices are identical (within numerical precision)")
        end
    else
        println("ERROR: Matrix sizes don't match!")
    end
    
    # Check row sums (should all be 1 for stochastic matrix)
    println("\nChecking row sums (should be 1.0):")
    T1_rowsums = [sum(T1[:,i]) for i in 1:size(T1,1)]
    T2_rowsums = [sum(T2[:,i]) for i in 1:size(T2,1)]
    
    println("T1 row sum range: [$(minimum(T1_rowsums)), $(maximum(T1_rowsums))]")
    println("T2 row sum range: [$(minimum(T2_rowsums)), $(maximum(T2_rowsums))]")
    
    # Compute steady states for both matrices
    println("\n" * "="^60)
    println("STEADY STATE ANALYSIS")
    println("="^60)
    
    # Helper functions
    function index_to_state(idx, Lx, Ly)
        idx = idx - 1
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end
    
    function config_to_string_2d(config, Lx, Ly)
        lines = String[]
        for r in 1:Lx
            row = join([config[r, c] == 1 ? "+" : "-" for c in 1:Ly], "")
            push!(lines, row)
        end
        return join(lines, " | ")
    end
    
    # Analyze T1 (exact_diag_toom.jl)
    println("\nT1 (exact_diag_toom.jl) Steady State:")
    T1_dense = Array(T1)
    F1 = eigen(T1_dense)
    steady_idx1 = findfirst(x -> isapprox(x, 1.0, atol=1e-10), F1.values)
    
    if !isnothing(steady_idx1)
        P1 = abs.(F1.vectors[:, steady_idx1]) ./ sum(abs.(F1.vectors[:, steady_idx1]))
        sorted_indices1 = sortperm(P1, rev=true)
        
        println("Top 5 configurations:")
        for i in 1:5
            idx = sorted_indices1[i]
            config = index_to_state(idx, Lx, Ly)
            prob = P1[idx]
            config_str = config_to_string_2d(config, Lx, Ly)
            @printf("%d: %s -> %.6e\n", i, config_str, prob)
        end
    else
        println("No steady state found!")
    end
    
    # Analyze T2 (toom_transfer_matrix.jl)
    println("\nT2 (toom_transfer_matrix.jl) Steady State:")
    T2_dense = Array(T2)
    F2 = eigen(T2_dense)
    steady_idx2 = findfirst(x -> isapprox(x, 1.0, atol=1e-10), F2.values)
    
    if !isnothing(steady_idx2)
        P2 = abs.(F2.vectors[:, steady_idx2]) ./ sum(abs.(F2.vectors[:, steady_idx2]))
        sorted_indices2 = sortperm(P2, rev=true)
        
        println("Top 5 configurations:")
        for i in 1:5
            idx = sorted_indices2[i]
            config = index_to_state(idx, Lx, Ly)
            prob = P2[idx]
            config_str = config_to_string_2d(config, Lx, Ly)
            @printf("%d: %s -> %.6e\n", i, config_str, prob)
        end
    else
        println("No steady state found!")
    end
    
    # Compare the steady state distributions
    if !isnothing(steady_idx1) && !isnothing(steady_idx2)
        P1 = abs.(F1.vectors[:, steady_idx1]) ./ sum(abs.(F1.vectors[:, steady_idx1]))
        P2 = abs.(F2.vectors[:, steady_idx2]) ./ sum(abs.(F2.vectors[:, steady_idx2]))
        
        steady_diff = maximum(abs.(P1 - P2))
        println("\nMaximum difference in steady state probabilities: $steady_diff")
    end
end

compare_transfer_matrices()