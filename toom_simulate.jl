using Random
using Statistics
using Plots

# ──────────────────────────────────────────────────────────────────────────────
# Majority helper (logical majority of {0,1})
majority(n, e, c) = (n & e) | (n & c) | (e & c)

# ──────────────────────────────────────────────────────────────────────────────
# Majority helper (logical majority of {0,1})
majority(n, e, c) = (n & e) | (n & c) | (e & c)

"""
    simulate_toom_nec(Lx, Ly, T; p=0.1, q=0.1,
                       x_antiperiodic=false, y_antiperiodic=false,
                       x_open=false, y_open=false,
                       init=:slab, seed=nothing)

Simulate <PERSON><PERSON>'s NEC rule (North–East–Center majority with p/q noise).

Boundary options (per axis):
- Periodic (default): wrap-around neighbors
- ABC (antiperiodic): wrap-around but flip the wrapped neighbor
- OBC (open): neighbor beyond the edge is fixed to + (0)

Notes:
- OBC and ABC cannot be both true on the same axis.
- With `y_open=true`, the model is effectively an Lx×(Ly+1) system with the
  (Ly+1)-th (northmost) row pinned to +. We evolve only the Lx×Ly bulk.

Returns: history, densities
- history has size (T+1, Lx, Ly); it stores only the bulk (the pinned row/col is not stored).
- densities is the mean of 1s in the evolving bulk at each time.
"""
function simulate_toom_nec(Lx::Int, Ly::Int, T::Int;
    p=0.1, q=0.1,
    x_antiperiodic::Bool=false, y_antiperiodic::Bool=false,
    x_open::Bool=false, y_open::Bool=false,
    init=:zeros, seed=nothing)

    # sanity checks
    if x_open && x_antiperiodic
        error("x_open=true conflicts with x_antiperiodic=true on the same axis.")
    end
    if y_open && y_antiperiodic
        error("y_open=true conflicts with y_antiperiodic=true on the same axis.")
    end

    seed === nothing || Random.seed!(seed)

    # State is indexed as state[x, y]
    state = zeros(Int8, Lx, Ly)
    if init === :random
        @inbounds for x in 1:Lx, y in 1:Ly
            state[x,y] = rand() < 0.5 ? 1 : 0
        end
    elseif init === :single
        state[clamp(div(Lx,2),1,Lx), clamp(div(Ly,2),1,Ly)] = 1
    elseif init === :zeros
        # do nothing
    else # :slab
        # Rightmost 3 columns = 1
        @inbounds for x in max(1,Lx-2):Lx, y in 1:Ly
            state[x,y] = 1
        end
    end

    history   = zeros(Int8, T+1, Lx, Ly)
    densities = zeros(Float64, T+1)

    # store t=0
    history[1, :, :] .= permutedims(state, (1,2))
    densities[1] = mean(state)

    new_state = similar(state)
    @inbounds for t in 1:T
        @inbounds for x in 1:Lx, y in 1:Ly
            # North neighbor
            if y < Ly
                north = state[x, y+1]
            else
                if y_open
                    north = 0  # fixed + row beyond the boundary
                else
                    # periodic or antiperiodic wrap
                    north = state[x, 1]
                    if y_antiperiodic
                        north = 1 - north
                    end
                end
            end

            # East neighbor
            if x < Lx
                east = state[x+1, y]
            else
                if x_open
                    east = 0  # fixed + column beyond the boundary
                else
                    # periodic or antiperiodic wrap
                    east = state[1, y]
                    if x_antiperiodic
                        east = 1 - east
                    end
                end
            end

            center = state[x, y]
            maj = majority(north, east, center)

            # p/q noise
            if maj == 1
                new_state[x,y] = (rand() < p) ? 0 : 1
            else
                new_state[x,y] = (rand() < q) ? 1 : 0
            end
        end
        state, new_state = new_state, state
        history[t+1, :, :] .= permutedims(state, (1,2))
        densities[t+1] = mean(state)
    end

    return history, densities
end


"""
    visualize_toom(history; fps=8, title="Toom NEC", folder="DATA", fname=nothing)

Create an animation. Axes match geometry: x→right, y↑up.
Saves a GIF and returns the file path.
"""
function visualize_toom(history; fps=8, title="Toom NEC", folder="DATA", fname=nothing)
    T1, Lx, Ly = size(history)
    mkpath(folder)
    fname === nothing && (fname = "toom_p$(p)_q$(q)_L$(Lx)x$(Ly)_T$(T1-1).gif")
    path = joinpath(folder, fname)
    anim = @animate for t in 1:T1
        # Plots.heatmap expects matrix indexed (row, col) ≈ (y, x).
        # history is stored as (Lx, Ly) in the last two dims; transpose for display.
        heatmap(1 .- transpose(reshape(view(history, t, :, :), Lx, Ly));
                c=:grays, clims=(0,1), aspect_ratio=:equal,
                xlabel="x (→ East)", ylabel="y (↑ North)",
                title="$title  (t=$(t-1))", legend=false, yflip=false)
    end
    gif(anim, path, fps=fps)
end

"""
    plot_density(densities; folder="DATA", fname=nothing)

Save a PNG of density vs time and return its path.
"""
function plot_density(densities; folder="DATA", fname=nothing)
    mkpath(folder)
    fname === nothing && (fname = "density_p$(p)_q$(q)_L$(Lx)x$(Ly)_T$(length(densities)-1).png")
    path = joinpath(folder, fname)
    plot(0:length(densities)-1, densities, xlabel="time", ylabel="density of 1s",
         title="Toom NEC density", legend=false)
    savefig(path)
    path
end

# ──────────────────────────────────────────────────────────────────────────────
# Example run

Lx, Ly = 40, 40
T = Lx*Ly
p, q = 0.01, 0.1
xanti, yanti = false, false

@time history, dens = simulate_toom_nec(Lx, Ly, T; p=p, q=q,
                                        y_open=true,  # adds fixed + row above y=Ly
                                        x_antiperiodic=false, x_open = false,
                                        # y_antiperiodic=yanti,
                                        init=:zeros, seed=42)
@time visualize_toom(history; fps=10, title="Toom NEC (p=$p, q=$q)")
@time plot_density(dens)

