# using Pkg; Pkg.add("Plots")  # ← run once if Plots isn't installed
using LinearAlgebra
using Printf
using Plots

# --- Model Definition ---

"""
    get_cell_transition_prob(new_val, old_val, neighbor_val, ε)

Probability of a single cell transitioning to `new_val` given its own `old_val`,
right neighbor `neighbor_val`, and parameter ε.
"""
function get_cell_transition_prob(new_val::Int, old_val::Int, neighbor_val::Int, ε::Float64)
    product = old_val * neighbor_val
    prob_becomes_1 = ε + (1 - ε) * product
    return new_val == 1 ? prob_becomes_1 : (1.0 - prob_becomes_1)
end

"""
    int_to_state(i, L) -> Vector{Int}

Convert integer `i` to its 0/1 state vector (length L), MSB at index 1.
"""
function int_to_state(i::Int, L::Int)
    return reverse(digits(i, base=2, pad=L))
end

"""
    build_transition_matrix(L, ε, boundary_condition) -> Matrix{Float64}

Construct the 2^L × 2^L transition matrix T with column-stochastic convention:
T[to, from] = P(to | from).
boundary_condition ∈ (:periodic, :antiperiodic).
"""
function build_transition_matrix(L::Int, ε::Float64, boundary_condition::Symbol)
    num_states = 2^L
    T = zeros(Float64, num_states, num_states)
    for from_idx in 0:(num_states - 1)
        state_from = int_to_state(from_idx, L)
        for to_idx in 0:(num_states - 1)
            state_to = int_to_state(to_idx, L)
            total_prob = 1.0
            for k in 1:L
                old_val = state_from[k]
                new_val = state_to[k]
                # Determine right neighbor's value based on BC
                if k == L
                    if boundary_condition == :periodic
                        neighbor_val = state_from[1]
                    elseif boundary_condition == :antiperiodic
                        neighbor_val = 1 - state_from[1]
                    else
                        error("Invalid boundary condition: ", boundary_condition)
                    end
                else
                    neighbor_val = state_from[k + 1]
                end
                cell_prob = get_cell_transition_prob(new_val, old_val, neighbor_val, ε)
                total_prob *= cell_prob
            end
            T[to_idx + 1, from_idx + 1] = total_prob
        end
    end
    return T
end

"""
    study_stavskaya_model(L, ε, boundary_condition)

Build T, print basic checks and leading eigenvalues, and list top stationary states.
"""
function study_stavskaya_model(L::Int, ε::Float64, boundary_condition::Symbol)
    if L > 8
        println("Warning: L=$L ⇒ matrix is $(2^L)×$(2^L). This may be heavy.")
    end
    println("--- Stavskaya Model Analysis ---")
    println("L=$L, ε=$ε, BC=$boundary_condition, #states=$(2^L)\n")

    println("Building transition matrix...")
    T = build_transition_matrix(L, ε, boundary_condition)

    if L <= 4
        println("Transition Matrix T (rounded):")
        display(round.(T, digits=3))
        println()
    else
        println("Transition matrix is too large to display.")
    end

    col_sums = sum(T, dims=1)
    if all(s -> isapprox(s, 1.0, atol=1e-9), col_sums)
        println("Matrix columns sum to 1. ✔\n")
    else
        println("Warning: Some columns do not sum to 1.\n")
    end

    println("Eigendecomposition...")
    eigen_decomp = eigen(T)
    eigenvals = eigen_decomp.values
    eigenvecs = eigen_decomp.vectors

    println("\n--- Leading Eigenvalues ---")
    sorted_indices = sortperm(abs.(eigenvals), rev=true)
    for i in 1:min(length(eigenvals), 10)
        idx = sorted_indices[i]
        @printf "λ_%-2d = %.6f + %.6fi   |λ|= %.6f\n" i real(eigenvals[idx]) imag(eigenvals[idx]) abs(eigenvals[idx])
    end
    println()

    target_eigenval_idx = findmin(abs.(eigenvals .- 1.0))[2]
    println("--- Stationary Distribution (eigenvector near λ=1) ---")
    stationary_vec = eigenvecs[:, target_eigenval_idx]
    stationary_dist = real.(stationary_vec)
    stationary_dist ./= sum(stationary_dist)

    if any(p -> p < -1e-9, stationary_dist)
        println("Warning: Negative entries in stationary_dist.")
    end

    println("State | Binary | Probability")
    println("------|--------|------------")
    dist_indices = sortperm(stationary_dist, rev=true)
    for i in 1:min(2*L, length(stationary_dist))
        idx = dist_indices[i]
        state_int = idx - 1
        state_binary = join(int_to_state(state_int, L))
        prob = stationary_dist[idx]
        @printf " %-4d | %-6s | %.6f\n" state_int state_binary prob
    end
end

# --- Observables ---

"""
    compute_entropy(p) -> Float64

Shannon entropy (nats) of distribution `p`.
"""
function compute_entropy(prob_dist::Vector{Float64})
    entropy = 0.0
    @inbounds for p in prob_dist
        if p > 1e-15
            entropy -= p * log(p)
        end
    end
    return entropy
end

"""
    compute_connected_correlation(prob_dist, L) -> Float64

Connected two-point corr: ⟨σ₁σ_L⟩ − ⟨σ₁⟩⟨σ_L⟩ with σ=2η−1.
"""
function compute_connected_correlation(prob_dist::Vector{Float64}, L::Int)
    mean_sigma1 = 0.0
    mean_sigmaL = 0.0
    mean_sigma1_sigmaL = 0.0
    @inbounds for (idx, prob) in enumerate(prob_dist)
        if prob > 1e-15
            state = int_to_state(idx - 1, L)
            sigma1 = 2 * state[1] - 1
            sigmaL = 2 * state[L] - 1
            mean_sigma1 += prob * sigma1
            mean_sigmaL += prob * sigmaL
            mean_sigma1_sigmaL += prob * sigma1 * sigmaL
        end
    end
    return mean_sigma1_sigmaL - mean_sigma1 * mean_sigmaL
end

"""
    marginal_distribution(prob_dist, L, subset) -> Vector{Float64}

Marginal over `subset` (1-based indices). Output length = 2^length(subset).
Bits are read in the order given by `subset`.
"""
function marginal_distribution(prob_dist::Vector{Float64}, L::Int, subset::Vector{Int})
    m = length(subset)
    if m == 0
        return [1.0]
    end
    marg = zeros(Float64, 2^m)
    @inbounds for (lin_idx, p) in enumerate(prob_dist)
        p <= 0.0 && continue
        state = int_to_state(lin_idx - 1, L)
        subidx = 0
        @inbounds for j in 1:m
            subidx = (subidx << 1) | state[subset[j]]
        end
        marg[subidx + 1] += p
    end
    return marg
end

"""
    compute_cmi_AC_given_B(prob_dist, L) -> Float64

Compute I(A:C|B) with A=site 1, C=site L, B=2..L-1 (nats).
For L=2, B is empty and this reduces to I(A:C).
"""
function compute_cmi_AC_given_B(prob_dist::Vector{Float64}, L::Int)
    A = [1]
    C = [L]
    B = (L >= 3) ? collect(2:(L-1)) : Int[]
    AB = vcat(A, B)
    BC = vcat(B, C)

    P_AB = marginal_distribution(prob_dist, L, AB)
    P_BC = marginal_distribution(prob_dist, L, BC)
    P_B  = marginal_distribution(prob_dist, L, B)

    H_AB  = compute_entropy(P_AB)
    H_BC  = compute_entropy(P_BC)
    H_B   = compute_entropy(P_B)
    H_ABC = compute_entropy(prob_dist)  # full system

    I = H_AB + H_BC - H_B - H_ABC
    return max(I, 0.0)  # clamp tiny negatives from FP error
end

# --- Time evolution ---

"""
    study_time_evolution(L, ε, boundary_condition, initial_dist, max_time)
      -> (times, entropies, correlations, cmi_vals, final_dist)

Evolve P(t+1)=T*P(t). Tracks entropy (nats), connected corr, and I(A:C|B).
"""
function study_time_evolution(L::Int, ε::Float64, boundary_condition::Symbol,
                              initial_dist::Vector{Float64}, max_time::Int)

    println("--- Time Evolution ---")
    println("L=$L, ε=$ε, BC=$boundary_condition, steps=$max_time\n")
    T = build_transition_matrix(L, ε, boundary_condition)

    times = Int[]
    entropies = Float64[]
    correlations = Float64[]
    cmi_vals = Float64[]

    current_dist = copy(initial_dist)

    # t = 0
    e0 = compute_entropy(current_dist)
    c0 = compute_connected_correlation(current_dist, L)
    i0 = compute_cmi_AC_given_B(current_dist, L)

    push!(times, 0); push!(entropies, e0); push!(correlations, c0); push!(cmi_vals, i0)

    println("Time | Entropy(nats) | Connected Corr       |   I(A:C|B)")
    println("-----|---------------|----------------------|------------")
    @printf("%4d | %13.6f | %20.6f | %10.6f\n", 0, e0, c0, i0)

    for t in 1:max_time
        current_dist = T * current_dist
        et = compute_entropy(current_dist)
        ct = compute_connected_correlation(current_dist, L)
        it = compute_cmi_AC_given_B(current_dist, L)

        push!(times, t); push!(entropies, et); push!(correlations, ct); push!(cmi_vals, it)
        @printf("%4d | %13.6f | %20.6f | %10.6f\n", t, et, ct, it)
    end

    return (times, entropies, correlations, cmi_vals, current_dist)
end

# --- Plotting ---

"""
    plot_time_series(times, entropies, correlations, cmi_vals; saveprefix=nothing, bits=false)

Plot entropy, connected correlation, and CMI vs time. If `bits=true`, entropy is
converted from nats to bits for display only. If `saveprefix` is set, PNGs are saved.
"""
function plot_time_series(times::Vector{Int}, entropies::Vector{Float64},
                          correlations::Vector{Float64}, cmi_vals::Vector{Float64};
                          saveprefix=nothing, bits=false)

    svals = bits ? entropies ./ log(2) : entropies
    ylab_entropy = bits ? "Shannon entropy (bits)" : "Shannon entropy (nats)"

    p1 = plot(times, svals, marker = :circle, xlabel="time", ylabel=ylab_entropy,
              title="Entropy vs Time", legend=false)
    p2 = plot(times, correlations, marker = :circle, xlabel="time",
              ylabel="Connected corr  ⟨σ₁σ_L⟩ − ⟨σ₁⟩⟨σ_L⟩",
              title="Connected Correlation vs Time", legend=false)
    p3 = plot(times, cmi_vals, marker = :circle, xlabel="time",
              ylabel="I(A:C|B) (nats)", title="CMI vs Time", legend=false)

    display(p1); display(p2); display(p3)

    if saveprefix !== nothing
        png(p1, "$(saveprefix)_entropy")
        png(p2, "$(saveprefix)_corr")
        png(p3, "$(saveprefix)_cmi")
    end
    return nothing
end

# --- Initial distributions ---

"""
    create_initial_distributions(L) -> Dict{String, Vector{Float64}}

A starter distribution (feel free to add more).
"""
function create_initial_distributions(L::Int)
    num_states = 2^L
    distributions = Dict{String, Vector{Float64}}()

    # # 1. Delta function at all zeros
    delta_zeros = zeros(Float64, num_states)
    delta_zeros[1] = 1.0  # State 0...0
    distributions["delta_zeros"] = delta_zeros
    
    # # 2. Delta function at all ones
    # delta_ones = zeros(Float64, num_states)
    # delta_ones[end] = 1.0  # State 1...1
    # distributions["delta_ones"] = delta_ones
    
    # 3. Uniform distribution
    # uniform = ones(Float64, num_states) / num_states
    # distributions["uniform"] = uniform


    # Half probability on all-ones, rest uniform
    # half_ones_uniform = fill(0.5 / (num_states - 1), num_states)
    # half_ones_uniform[end] = 0.5
    # distributions["half_ones_uniform"] = half_ones_uniform

    return distributions
end

# --- Main ---

const L_EVOL = 10
const ε_EVOL = 0.1
const BOUNDARY_EVOL = :periodic
const MAX_TIME = 20

println("="^60)
println("STAVSKAYA MODEL: TIME EVOLUTION OF ENTROPY, CORR, AND CMI")
println("="^60)

initial_dists = create_initial_distributions(L_EVOL)

for (name, init_dist) in initial_dists
    println("\n" * "="^40)
    println("Initial Distribution: $name")
    println("="^40)

    times, entropies, correlations, cmi_vals, final_dist = study_time_evolution(
        L_EVOL, ε_EVOL, BOUNDARY_EVOL, init_dist, MAX_TIME
    )

    println("\nFinal entropy (nats): $(entropies[end])")
    println("Final connected corr: $(correlations[end])")
    println("Final I(A:C|B) (nats): $(cmi_vals[end])")

    println("\nTop 5 final configurations:")
    sorted_indices = sortperm(final_dist, rev=true)
    for i in 1:min(5, length(sorted_indices))
        idx = sorted_indices[i]
        state_int = idx - 1
        state_binary = join(int_to_state(state_int, L_EVOL))
        prob = final_dist[idx]
        @printf(" %s -> %.6f\n", state_binary, prob)
    end

    # Plots (set bits=true for entropy in bits; set saveprefix to save PNGs)
    plot_time_series(times, entropies, correlations, cmi_vals;
                     bits=false, saveprefix="stavskaya_$(name)")
end
