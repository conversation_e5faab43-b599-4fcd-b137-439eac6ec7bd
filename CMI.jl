using Plots
using Statistics, SpecialFunctions  # for binomcoeff
function cmi(p,q,L)
    a,b = 1-q, q
    c,d = p, 1-p
    t(k,m) = 0.5*((a)^(k-m)*(b)^m + (c)^(k-m)*(d)^m)
    H(k) = -sum(binomial(k,m)*t(k,m)*log2(t(k,m)) for m in 0:k)
    return 2H(L-1) - H(L) - H(L-2)
end

using StatsFuns: logaddexp   # for a stable log-sum-exp
"""
    cmi_chain(p, q, L)

Conditional mutual information I(Y₁:Y_L | Y₂⋯Y_{L-1}) for
an L-site chain that starts from the GHZ-mixture and then
goes through the single-qubit channel
    |0⟩→|0⟩ with 1-q,  |0⟩→|1⟩ with q
    |1⟩→|1⟩ with 1-p,  |1⟩→|0⟩ with p.
Works reliably up to at least L≈10⁵.
"""
function cmi_chain(p, q, L)
    @assert 2 ≤ L                "Need at least two sites"
    a, b = 1-q, q
    c, d =    p, 1-p
    ln2 = log(2)

    ln_a, ln_b = log(a), log(b)
    ln_c, ln_d = log(c), log(d)
    ln_half = log(0.5)

    ln_binom = 0.0               # ln( (L-2 choose 0) )
    cmi = 0.0

    for m in 0:(L-2)
        # update ln((L-2 choose m)) on the fly
        if m > 0
            ln_binom += log(L-2 - (m-1)) - log(m)
        end

        ln_f0 = (L-2-m)*ln_a + m*ln_b          # ln a^{L-2-m} b^m
        ln_f1 = (L-2-m)*ln_c + m*ln_d          # ln c^{L-2-m} d^m
        ln_sumf = logaddexp(ln_f0, ln_f1)      # ln(f0+f1)

        # P(M=m)
        ln_PM = ln_half + ln_binom + ln_sumf
        PM    = exp(ln_PM)

        # posterior P(X=0 | M=m)
        P0 = 1 / (1 + exp(ln_f1 - ln_f0))
        P1 = 1 - P0

        # single-spin marginal  π
        π0 = P0*a + P1*c
        π1 = 1 - π0
        H1 = -(π0*log(π0) + π1*log(π1)) / ln2      # binary entropy

        # joint probabilities of the two ends
        P00 = P0*a*a + P1*c*c
        P01 = P0*a*b + P1*c*d
        P11 = P0*b*b + P1*d*d
        Hjoint = -( P00*log(P00) + 2P01*log(P01) + P11*log(P11) ) / ln2

        cmi += PM * ( 2H1 - Hjoint )
    end
    return cmi
end



Ls = [1000, 2000,3000]

q = 0.7
# pc = 1-q
# rlist = 0.9*pc:0.01*pc:1.1*pc
rlist = 0:0.01:1

for L in Ls

CMI_analytics = Float64[]

for r in rlist
    # println("\n" * "="^80)
    # CMI= cmi(r, q, L)
    CMI = cmi_chain(r, q, L)
    push!(CMI_analytics, CMI)
end

if L == Ls[1]
    # @show L
    plot(rlist, CMI_analytics,
        marker=:circle, linewidth=2, label = "L = $L")
else
    plot!(rlist, CMI_analytics,
        marker=:circle, linewidth=2, label = "L = $L")
end
end

plot!(xlabel="p", ylabel="I(A:C|B)",
        title="CMI vs Noise (q = $q)")


