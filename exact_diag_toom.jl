using LinearAlgebra
using SparseArrays
using Printf
using Statistics
using Arpack

# --- State Space Helper Functions (Using your row-major convention) ---

   


"""
Converts a grid configuration back to an integer index.
"""
function state_to_index(state::Matrix{Int}, Lx::Int, Ly::Int)
    idx = 1
    for i in 1:Lx, j in 1:Ly
        idx += state[i, j] * 2^((i-1)*Ly + (j-1))
    end
    return idx
end


# --- Main Simulation and Verification Logic ---

"""
Constructs the global transition matrix for the Toom model.
"""
function create_toom_transfer_matrix_test(Lx::Int, Ly::Int, p::Float64, q::Float64)
    N = Lx * Ly
    num_states = 2^N
    
    # Use sparse matrix for efficiency
    I = Int[]
    J = Int[]
    V = Float64[]

    println("1. Constructing the global transition matrix (T)...")
    
    # Majority function for binary spins (0, 1)
    majority(n, e, c) = (n & e) | (n & c) | (e & c)
    
    # Enumerate all transitions since the system is small
    for idx_from in 1:num_states
        state_from = index_to_state(idx_from, Lx, Ly)
        
        # 1. Get the deterministic next state based on majority votes
        deterministic_next_state = zeros(Int, Lx, Ly)
        for x in 1:Lx, y in 1:Ly
            north = state_from[mod1(x-1, Lx), y]
            east  = state_from[x, mod1(y+1, Ly)]
            center = state_from[x, y]
            deterministic_next_state[x,y] = majority(north, east, center)
        end

        # 2. Iterate over all 2^N possible error configurations
        for error_config_idx in 0:(2^N - 1)
            final_state = copy(deterministic_next_state)
            log_prob = 0.0

            for i in 1:Lx, j in 1:Ly
                k = (i-1)*Ly + (j-1) # Linear index for the error bit
                error_occurred = ((error_config_idx >> k) & 1) == 1
                
                site_det_state = deterministic_next_state[i, j]

                if error_occurred
                    final_state[i, j] = 1 - site_det_state
                    if site_det_state == 1 # Majority was '+', error is a flip to '-'
                        log_prob += log(q)
                    else # Majority was '-', error is a flip to '+'
                        log_prob += log(p)
                    end
                else
                    # No error occurred
                    if site_det_state == 1 # Majority was '+', no flip
                        log_prob += log(1 - q)
                    else # Majority was '-', no flip
                        log_prob += log(1 - p)
                    end
                end
            end
            
            idx_to = state_to_index(final_state, Lx, Ly)
            prob = exp(log_prob)

            if prob > 0
                push!(I, idx_to)
                push!(J, idx_from)
                push!(V, prob)
            end
        end
    end
    
    T = sparse(I, J, V, num_states, num_states)
    println("   Matrix construction complete. Size: $(size(T)), Non-zero elements: $(nnz(T))")
    return T
end

"""
Analyzes the transfer matrix to find the invariant measure and verify theoretical predictions.
"""
function analyze_transfer_matrix(T::SparseMatrixCSC{Float64, Int}, Lx::Int, Ly::Int, p::Float64, q::Float64)
    function config_to_string_2d(config, Lx, Ly)
        lines = String[]
        for r in 1:Lx
            row = join([config[r, c] == 1 ? "+" : "-" for c in 1:Ly], "")
            push!(lines, row)
        end
        return join(lines, " | ")
    end
    N = Lx * Ly
    num_states = 2^N

    println("\n2. Finding invariant measure with LinearAlgebra.eigen...")
    T_dense = Array(T)

    F_eigen = eigen(T_dense)
    vals_eigen = F_eigen.values
    vecs_eigen = F_eigen.vectors
    # @show vals_eigen
    # steady_idx_eigen = findfirst(x -> isapprox(x, 1.0, atol=1e-12), vals_eigen)
    steady_idx_eigen = sortperm(abs.(vals_eigen), rev=true)[1]

    if isnothing(steady_idx_eigen)
        println("Direct solver could not find the steady state.")
        return
    end

    P_exact = abs.(vecs_eigen[:, steady_idx_eigen]) ./ sum(abs.(vecs_eigen[:, steady_idx_eigen]))
    println("   Exact stationary distribution (P_exact) found.")

    # --- Construct the theoretical phase distributions ---
    println("\n3. Constructing Theoretical Model...")
    P_th_plus = zeros(num_states)
    P_th_minus = zeros(num_states)
    
    for i_idx in 1:num_states
        config = index_to_state(i_idx, Lx, Ly)
        eta_minus = count(x -> x == 0, config) # Number of '-' spins (0)
        eta_plus = N - eta_minus              # Number of '+' spins (1)

        P_th_plus[i_idx] = ((1 - q)^eta_plus) * (q^eta_minus)
        P_th_minus[i_idx] = ((1 - p)^eta_minus) * (p^eta_plus)
    end
    
    P_th_plus ./= sum(P_th_plus)
    P_th_minus ./= sum(P_th_minus)
    println("   Theoretical phase distributions (P⁺ and P⁻) constructed.")

    # --- Fit weights and compare with analytical formula ---
    println("\n4. Verifying Weights w⁺ and w⁻...")
    A = P_th_plus - P_th_minus
    b = P_exact - P_th_minus
    w_plus_fit = (A' * b) / (A' * A)
    w_minus_fit = 1 - w_plus_fit
    
    @printf("   - Optimal fitted weights: w⁺_fit = %.6f, w⁻_fit = %.6f\n", w_plus_fit, w_minus_fit)

    Nc_effective = Ly*Lx # From analytical derivation
    @printf("   - Using analytical critical nucleus size Nc = %.1f\n", Nc_effective)

    w_plus_theory = p^Nc_effective / (p^Nc_effective + q^Nc_effective)
    w_minus_theory = q^Nc_effective / (p^Nc_effective + q^Nc_effective)

    @printf("   - Comparison of fitted vs theoretical weights:\n")
    @printf("     - Fitted weights:      w⁺=%.6f, w⁻=%.6f\n", w_plus_fit, w_minus_fit)
    @printf("     - Theory (from Nc= Ly*Lx):  w⁺=%.6f, w⁻=%.6f\n", w_plus_theory, w_minus_theory)
    
    # --- Final Verification: L2 norm and top configurations ---
    println("\n5. Final Verification...")
    P_theory = w_plus_theory * P_th_plus + w_minus_theory * P_th_minus
    l2_norm_difference = norm(P_exact - P_theory)
    
    @printf("   - L2 norm ||P_exact - P_theory|| = %.4e\n", l2_norm_difference)
    println("   - Agreement: ", l2_norm_difference < 1e-3 ? "Excellent" : "Fair")
    
    s = 5 # Number of top configurations to show
    println("\n   - Top $s most probable configurations:")
    
    function config_to_string_2d(config, Lx, Ly)
        lines = String[]
        for r in 1:Lx
            row = join([config[r, c] == 1 ? "+" : "-" for c in 1:Ly], " ")
            push!(lines, row)
        end
        return join(lines, " | ")
    end
    
    exact_sorted = sortperm(P_exact, rev=true)
    theory_sorted = sortperm(P_theory, rev=true)
    
    println("     P_exact (from diagonalization):")
    for rank in 1:s
        idx = exact_sorted[rank]
        @printf("     %d: (prob=%.4e) %s\n", rank, P_exact[idx], config_to_string_2d(index_to_state(idx, Lx, Ly), Lx, Ly))
    end
    
    println("\n     P_theory (from analytical solution):")
    for rank in 1:s
        idx = theory_sorted[rank]
        @printf("     %d: (prob=%.4e) %s\n", rank, P_theory[idx], config_to_string_2d(index_to_state(idx, Lx, Ly), Lx, Ly))
    end

    # --- Verify the average magnetization ---
    println("\n6. Magnetization Verification...")
    exact_magnetization = 0.0
    for idx in 1:num_states
        config = index_to_state(idx, Lx, Ly)
        mag_config = 2 .* config .- 1 # Convert 0/1 to -1/+1
        exact_magnetization += P_exact[idx] * mean(mag_config)
    end
    
    theoretical_magnetization = w_plus_theory * (1 - 2*q) + w_minus_theory * (-1 + 2*p)
    
    @printf("   - Exact numerical magnetization: %.6f\n", exact_magnetization)
    @printf("   - Reconstructed theoretical magnetization: %.6f\n", theoretical_magnetization)
    @printf("   - Agreement: %s\n", isapprox(exact_magnetization, theoretical_magnetization, atol=1e-4) ? "Excellent" : "Fair")
    println("----------------------------------------")
end


function main()
    # Parameters for the simulation
    Lx = 3
    Ly = 3
    p = 0.02
    q = 0.01

    # Run the verification
    println("Creating transfer matrix for $(Lx)×$(Ly) grid with p=$p, q=$q...")
    T = create_toom_transfer_matrix_test(Lx, Ly, p, q)



    ## another method to analyze the steady state

    T1 =copy(T)
    # Analyze T1 (exact_diag_toom.jl)
    println("\nT1 (exact_diag_toom.jl) Steady State:")
    T1_dense = Array(T1)
    F1 = eigen(T1_dense)
    steady_idx1 = findfirst(x -> isapprox(x, 1.0, atol=1e-10), F1.values)
    
    if !isnothing(steady_idx1)
        P1 = abs.(F1.vectors[:, steady_idx1]) ./ sum(abs.(F1.vectors[:, steady_idx1]))
        sorted_indices1 = sortperm(P1, rev=true)
        
        println("Top 5 configurations:")
        for i in 1:5
            idx = sorted_indices1[i]
            config = index_to_state(idx, Lx, Ly)
            prob = P1[idx]
            config_str = config_to_string_2d(config, Lx, Ly)
            @printf("%d: %s -> %.6e\n", i, config_str, prob)
        end
    else
        println("No steady state found!")
    end

    println("\nTransfer matrix size: $(size(T))")
    println("Number of non-zero elements: $(nnz(T))")

    # Call the corrected analysis function
    analyze_transfer_matrix(T, Lx, Ly, p, q)

    println("\nAnalysis complete!")
end

main()
