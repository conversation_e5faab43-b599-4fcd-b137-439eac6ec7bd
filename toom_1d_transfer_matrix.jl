using LinearAlgebra
using SparseArrays
using Printf
using Statistics
using Arpack

"""
    create_toom_transfer_matrix(Lx, Ly, p, q)

Create the transfer matrix for Too<PERSON>'s NEC model with given parameters.
The transfer matrix has dimensions 2^(Lx*Ly) × 2^(Lx*Ly).

Parameters:
- Lx, Ly: Grid dimensions
- p: Probability of error when majority is '-' (0)
- q: Probability of error when majority is '+' (1)

Returns a sparse matrix representing the transfer operator.
"""
function create_toom_transfer_matrix(Lx, Ly, p, q)
    # Total number of states
    n_states = 2^(Lx * Ly)
    N = Lx * Ly
    
    # Use sparse matrix for efficiency
    # Pre-allocating vectors for sparse matrix construction
    I = Int[]
    J = Int[]
    V = Float64[]
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:Lx, j in 1:Ly
            idx += state[i, j] * 2^((i-1)*Ly + (j-1))
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end
    
    # Majority function for binary spins (0, 1)
    majority(n, e, c) = (n & e) | (n & c) | (e & c)
    
    # Build the transfer matrix
    println("Building transfer matrix (this may take a while)...")
    
    # Enumerate all transitions since the system is small
    for idx_from in 1:n_states
        state_from = index_to_state(idx_from)
        
        # This part is complex. A more efficient way is to calculate the single
        # resulting state from a deterministic update and then consider all
        # possible noise flips from there. Let's do that.

        # 1. Get the deterministic next state
        deterministic_next_state = zeros(Int, Lx, Ly)
        for x in 1:Lx, y in 1:Ly
            # --- CORRECTION 1: Correct Neighbor Indexing (North-East-Center) ---
            north = state_from[mod1(x-1, Lx), y]
            east  = state_from[x, mod1(y+1, Ly)]
            center = state_from[x, y]
            deterministic_next_state[x,y] = majority(north, east, center)
        end

        # 2. Iterate over all possible error configurations (2^N of them)
        for error_config_idx in 0:(2^N - 1)
            final_state = copy(deterministic_next_state)
            log_prob = 0.0

            for k in 1:N
                # Check if an error occurred at site k
                error_occurred = ((error_config_idx >> (k-1)) & 1) == 1
                
                # Get the deterministic state at this site
                site_det_state = deterministic_next_state[k]

                if error_occurred
                    # Flip the spin
                    final_state[k] = 1 - site_det_state
                    # --- CORRECTION 2: Correct Noise Probabilities (p and q) ---
                    if site_det_state == 1 # Majority was '+', error is a flip to '-'
                        log_prob += log(q)
                    else # Majority was '-', error is a flip to '+'
                        log_prob += log(p)
                    end
                else
                    # No flip occurred
                    if site_det_state == 1 # Majority was '+', no flip
                        log_prob += log(1 - q)
                    else # Majority was '-', no flip
                        log_prob += log(1 - p)
                    end
                end
            end
            
            idx_to = state_to_index(final_state)
            prob = exp(log_prob)

            if prob > 0
                push!(I, idx_to)
                push!(J, idx_from)
                push!(V, prob)
            end
        end
    end
    
    return sparse(I, J, V, n_states, n_states)
end

"""
    analyze_transfer_matrix(T, Lx, Ly)

Analyze the transfer matrix to find steady states and relaxation times.
"""
function analyze_transfer_matrix(T, Lx, Ly)
    println("\nAnalyzing transfer matrix...")

    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end

    # Helper function to display a 2D configuration
    function display_config(state)
        # Convert 0/1 to readable characters
        display_map = Dict(0 => " - ", 1 => " + ")
        for i in 1:Lx
            row_str = ""
            for j in 1:Ly
                row_str *= display_map[state[i, j]]
            end
            println(row_str)
        end
    end

    # Calculate eigenvalues and eigenvectors using Arpack for sparse matrices
    println("Calculating eigenvalues...")
    # We want the eigenvalue with the largest magnitude, which should be 1.
    vals, vecs = eigs(T, nev=2, which=:LM)

    println("\n=== Eigenvalue Analysis ===")
    println("Largest eigenvalues (by magnitude):")
    for i in 1:length(vals)
        λ = vals[i]
        @printf("λ%d = %8.6f + %8.6fi (|λ| = %8.6f)\n", i, real(λ), imag(λ), abs(λ))
    end

    # Find steady state (eigenvector for eigenvalue ≈ 1)
    steady_idx = findfirst(x -> isapprox(x, 1.0), vals)
    
    if !isnothing(steady_idx)
        println("\n=== Steady State Analysis ===")
        steady_state_vec = vecs[:, steady_idx]
        # Normalize to a probability distribution
        P_exact = abs.(steady_state_vec) ./ sum(abs.(steady_state_vec))

        # Show most probable configurations
        println("Most probable configurations in the exact steady state:")
        sorted_indices = sortperm(P_exact, rev=true)

        for i in 1:min(5, length(sorted_indices))
            idx = sorted_indices[i]
            config = index_to_state(idx)
            prob = P_exact[idx]
            @printf("\nConfiguration %d (probability: %.6f):\n", i, prob)
            display_config(config)
        end

        # Compute average magnetization
        avg_magnetization = 0.0
        for idx in 1:length(P_exact)
            config = index_to_state(idx)
            # Convert 0/1 to -1/+1 for magnetization
            mag_config = 2 .* config .- 1
            avg_magnetization += P_exact[idx] * mean(mag_config)
        end
        @printf("\nAverage magnetization in steady state: %.6f\n", avg_magnetization)
    else
        println("\nNo steady state found with eigenvalue 1.")
    end
end

# Example usage
Lx, Ly = 3, 3  # Small grid for demonstration
p = 0.001
q = 0.0008

println("Creating transfer matrix for $(Lx)×$(Ly) grid...")
T = create_toom_transfer_matrix(Lx, Ly, p, q)

println("\nTransfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

# Analyze the transfer matrix
analyze_transfer_matrix(T, Lx, Ly)

println("\nAnalysis complete!")
