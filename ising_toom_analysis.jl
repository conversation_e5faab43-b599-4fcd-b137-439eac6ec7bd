# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
#
#   Stavskaya Model Transition Matrix Analysis in Julia
#
#   This script constructs the full transition matrix for the 1D Stavskaya
#   probabilistic cellular automaton (PCA) with selectable boundary
#   conditions and analyzes its properties. This version includes both
#   1-biased (ε) and 0-biased (δ) noise parameters.
#
#   Author: Gemini
#   Date:   August 10, 2025
#
# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #

import Pkg
# Ensure LinearAlgebra is available
using LinearAlgebra
using Printf

# --- Model Definition ---
# The model is a 1D probabilistic cellular automaton with two states {0, 1}.
# The state of cell `i` at time `t+1` is updated based on its state and its
# right neighbor's state at time `t`.
#
# First, a deterministic state `d` is calculated:
#   d = η_i(t) * η_{i+1}(t)
#
# Then, noise is applied:
#   - 1-biased noise (ε): If d=0, the cell flips to 1 with probability ε.
#   - 0-biased noise (δ): If d=1, the cell flips to 0 with probability δ.
#
# This implies the following transition probabilities for a single cell `i`:
#   P(η_i(t+1) = 1) = d * (1 - δ) + (1 - d) * ε
#   P(η_i(t+1) = 0) = d * δ + (1 - d) * (1 - ε)
#
# Boundary Conditions:
#   - Periodic: The neighbor of cell L is cell 1.
#   - Anti-periodic: The neighbor of cell L is the *opposite* of cell 1's state (1 - η_1).

"""
    get_cell_transition_prob(new_val, old_val, neighbor_val, ε, δ)

Calculates the probability of a single cell transitioning to `new_val`.
"""
function get_cell_transition_prob(new_val::Int, old_val::Int, neighbor_val::Int, ε::Float64, δ::Float64)
    # Calculate the deterministic part of the rule
    deterministic_state = old_val * neighbor_val
    
    # Apply 1-biased (ε) and 0-biased (δ) noise
    local prob_becomes_1::Float64
    if deterministic_state == 1
        # If deterministic state is 1, it can flip to 0 with probability δ
        prob_becomes_1 = 1.0 - δ
    else # deterministic_state == 0
        # If deterministic state is 0, it can flip to 1 with probability ε
        prob_becomes_1 = ε
    end
    
    if new_val == 1
        return prob_becomes_1
    else # new_val == 0
        return 1.0 - prob_becomes_1
    end
end

"""
    int_to_state(i::Int, L::Int)

Converts an integer `i` to its binary array representation of length `L`.
"""
function int_to_state(i::Int, L::Int)
    return reverse(digits(i, base=2, pad=L))
end

"""
    build_transition_matrix(L::Int, ε::Float64, δ::Float64, boundary_condition::Symbol)

Constructs the transition matrix for the Stavskaya model.

# Arguments
- `L::Int`: The size of the 1D lattice.
- `ε::Float64`: The 1-biased noise parameter (0 -> 1 flips).
- `δ::Float64`: The 0-biased noise parameter (1 -> 0 flips).
- `boundary_condition::Symbol`: `:periodic` or `:antiperiodic`.

# Returns
- `Matrix{Float64}`: A (2^L x 2^L) transition matrix.
"""
function build_transition_matrix(L::Int, ε::Float64, δ::Float64, boundary_condition::Symbol)
    num_states = 2^L
    T = zeros(Float64, num_states, num_states)

    # Note: States are represented by integers 0 to num_states-1.
    # Julia matrices are 1-indexed, so we map state `i` to column `i+1`.
    
    for from_idx in 0:(num_states - 1)
        state_from = int_to_state(from_idx, L)
        
        for to_idx in 0:(num_states - 1)
            state_to = int_to_state(to_idx, L)
            
            total_prob = 1.0
            
            for k in 1:L
                old_val = state_from[k]
                new_val = state_to[k]
                
                # Determine the right neighbor's value based on boundary conditions
                local neighbor_val::Int
                if k == L # If we are at the last cell, the neighbor wraps around
                    if boundary_condition == :periodic
                        neighbor_val = state_from[1]
                    elseif boundary_condition == :antiperiodic
                        neighbor_val = 1 - state_from[1] # Flipped state
                    else
                        error("Invalid boundary condition: ", boundary_condition)
                    end
                else # For all other cells, the neighbor is k+1
                    neighbor_val = state_from[k + 1]
                end
                
                cell_prob = get_cell_transition_prob(new_val, old_val, neighbor_val, ε, δ)
                total_prob *= cell_prob
            end
            
            T[to_idx + 1, from_idx + 1] = total_prob
        end
    end
    
    return T
end

"""
    study_stavskaya_model(L::Int, ε::Float64, δ::Float64, boundary_condition::Symbol)

Main function to build, analyze, and print information about the model.
"""
function study_stavskaya_model(L::Int, ε::Float64, δ::Float64, boundary_condition::Symbol)
    if L > 8
        println("Warning: Lattice size L=$L results in a $(2^L)x$(2^L) matrix.")
        println("This may be computationally expensive. Proceeding anyway...")
    end
    
    println("--- Stavskaya Model Analysis (Right-Neighbor Rule) ---")
    println("Lattice size (L): $L")
    println("1-biased noise (ε): $ε")
    println("0-biased noise (δ): $δ")
    println("Boundary Condition: $boundary_condition")
    println("Total number of states: $(2^L)\n")
    
    # 1. Build the transition matrix
    println("Building transition matrix...")
    T = build_transition_matrix(L, ε, δ, boundary_condition)
    
    if L <= 4
        println("Transition Matrix T (T[j,i] = P(to j | from i)):")
        display(round.(T, digits=3))
        println()
    else
        println("Transition matrix is too large to display.")
    end

    # Verify that columns sum to 1
    col_sums = sum(T, dims=1)
    if all(s -> isapprox(s, 1.0, atol=1e-9), col_sums)
        println("Matrix columns correctly sum to 1.\n")
    else
        println("Warning: Matrix columns do not all sum to 1. Check logic.\n")
    end

    # 2. Analyze the matrix using eigendecomposition
    println("Performing eigendecomposition...")
    eigen_decomp = eigen(T)
    eigenvals = eigen_decomp.values
    eigenvecs = eigen_decomp.vectors

    println("\n--- Eigenvalues ---")
    println("The eigenvalues determine the dynamics and convergence.")
    sorted_indices = sortperm(abs.(eigenvals), rev=true)
    for i in 1:min(length(eigenvals), 10)
        idx = sorted_indices[i]
        @printf "λ_%-2d = %.6f + %.6fi   (Magnitude: %.6f)\n" i real(eigenvals[idx]) imag(eigenvals[idx]) abs(eigenvals[idx])
    end
    if length(eigenvals) > 10
        println("...")
    end
    println()

    # 3. Find the stationary distribution
    target_eigenval_idx = findmin(abs.(eigenvals .- 1.0))[2]
    
    println("--- Stationary Distribution ---")
    println("The stationary distribution is the eigenvector for eigenvalue λ ≈ 1.0.")
    
    stationary_vec = eigenvecs[:, target_eigenval_idx]
    stationary_dist = real.(stationary_vec) ./ sum(real.(stationary_vec))

    if any(p -> p < -1e-9, stationary_dist)
        println("Warning: Negative probabilities found in stationary distribution.")
    end
    
    println("State | Binary Rep. | Probability")
    println("------|-------------|------------")
    
    dist_indices = sortperm(stationary_dist, rev=true)

    # Print the most probable states, up to a reasonable limit
    num_to_print = min(length(stationary_dist), 2*L)
    for i in 1:num_to_print
        idx = dist_indices[i]
        state_int = idx - 1
        state_binary = join(int_to_state(state_int, L))
        prob = stationary_dist[idx]
        @printf " %-4d | %-11s | %.6f\n" state_int state_binary prob
    end
     if length(stationary_dist) > num_to_print
        println("... and $(length(stationary_dist) - num_to_print) more states.")
    end
end

# --- Main Execution ---
# Set the parameters for the model
const L = 10
const ε = 0.000001    # 1-biased noise: probability of 0 -> 1 flip after deterministic step
const δ = 0.000001   # 0-biased noise: probability of 1 -> 0 flip after deterministic step
const BOUNDARY_CONDITION = :antiperiodic # Options: :periodic, :antiperiodic

# Run the analysis
study_stavskaya_model(L, ε, δ, BOUNDARY_CONDITION)
