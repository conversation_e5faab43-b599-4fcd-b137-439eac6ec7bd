using Printf
using Statistics

"""
    update_step!(grid, p, q)

Performs a single time step of the noisy Toom model simulation on the `grid`.
"""
function update_step!(grid, p, q)
    L = size(grid, 1)
    # Create a copy to store the state before updates, to ensure all updates are synchronous
    old_grid = copy(grid)

    for i in 1:L, j in 1:L
        # Periodic boundary conditions
        north = old_grid[mod1(i - 1, L), j]
        east = old_grid[i, mod1(j + 1, L)]
        center = old_grid[i, j]

        # 1. Deterministic majority vote
        majority_vote = sign(north + east + center)

        # 2. Apply probabilistic noise
        if majority_vote == 1
            # With probability q, flip to -1
            if rand() < q
                grid[i, j] = -1
            else
                grid[i, j] = 1
            end
        else # majority_vote == -1
            # With probability p, flip to +1
            if rand() < p
                grid[i, j] = 1
            else
                grid[i, j] = -1
            end
        end
    end
end

"""
    run_simulation_and_verify(L, p, q, steps, burn_in, initial_state)

Runs the Toom model simulation and verifies the results against the theoretical predictions.
"""
function run_simulation_and_verify(L, p, q, steps, burn_in, initial_state)
    if initial_state == 1
        println("--- Verifying the 'Plus' Phase (starting with all +1) ---")
    else
        println("--- Verifying the 'Minus' Phase (starting with all -1) ---")
    end
    
    @printf("Parameters: L=%d, p=%.4f, q=%.4f, Steps=%d, Burn-in=%d\n\n", L, p, q, steps, burn_in)

    grid = fill(initial_state, (L, L))
    total_sites = L^2

    magnetizations = Float64[]
    error_counts = Int[]

    # Main simulation loop
    for step in 1:steps
        update_step!(grid, p, q)

        # Start collecting data after the burn-in period
        if step > burn_in
            # Calculate total magnetization for this step
            push!(magnetizations, sum(grid) / total_sites)

            # Calculate the number of "error" spins
            if initial_state == 1
                push!(error_counts, count(x -> x == -1, grid)) # η(S) for the plus phase
            else
                push!(error_counts, count(x -> x == 1, grid)) # η(S) for the minus phase
            end
        end
    end

    # --- Analysis and Verification ---

    # 1. Verify Average Magnetization
    numerical_avg_magnetization = mean(magnetizations)
    
    if initial_state == 1
        theoretical_magnetization = 1 - 2 * q
        println("1. Average Magnetization Verification:")
        @printf("   - Numerical Result: <S_r> = %.6f\n", numerical_avg_magnetization)
        @printf("   - Theoretical Prediction (1 - 2q): %.6f\n", theoretical_magnetization)
        @printf("   - Agreement: %s\n\n", (isapprox(numerical_avg_magnetization, theoretical_magnetization, atol=0.01) ? "Excellent" : "Fair"))
    else
        theoretical_magnetization = -1 + 2 * p
        println("1. Average Magnetization Verification:")
        @printf("   - Numerical Result: <S_r> = %.6f\n", numerical_avg_magnetization)
        @printf("   - Theoretical Prediction (-1 + 2p): %.6f\n", theoretical_magnetization)
        @printf("   - Agreement: %s\n\n", (isapprox(numerical_avg_magnetization, theoretical_magnetization, atol=0.01) ? "Excellent" : "Fair"))
    end

    # 2. Verify Distribution of Error Spins
    # We check the average number of error spins against the binomial expectation.
    numerical_avg_errors = mean(error_counts)
    
    println("2. Distribution of Error Spins Verification:")
    if initial_state == 1
        # For the plus phase, errors are -1 spins, occurring with probability q
        theoretical_avg_errors = total_sites * q
        @printf("   - Average number of '-1' spins (Numerical): %.4f\n", numerical_avg_errors)
        @printf("   - Theoretical Expectation (L² * q):       %.4f\n", theoretical_avg_errors)
        @printf("   - Agreement: %s\n", (isapprox(numerical_avg_errors, theoretical_avg_errors, atol=sqrt(total_sites * q * (1-q))) ? "Excellent" : "Fair"))
    else
        # For the minus phase, errors are +1 spins, occurring with probability p
        theoretical_avg_errors = total_sites * p
        @printf("   - Average number of '+1' spins (Numerical): %.4f\n", numerical_avg_errors)
        @printf("   - Theoretical Expectation (L² * p):       %.4f\n", theoretical_avg_errors)
        @printf("   - Agreement: %s\n", (isapprox(numerical_avg_errors, theoretical_avg_errors, atol=sqrt(total_sites * p * (1-p))) ? "Excellent" : "Fair"))
    end
    println("-------------------------------------------------------\n")
end

function main()
    # Simulation parameters
    L = 10          # Lattice size (L x L)
    steps = 20000   # Total simulation steps
    burn_in = 2000  # Steps to discard to reach steady state

    # Low-noise parameters
    p = 0.01
    q = 0.02
    
    # Run verification for the "plus" phase
    run_simulation_and_verify(L, p, q, steps, burn_in, 1)

    # Run verification for the "minus" phase
    run_simulation_and_verify(L, p, q, steps, burn_in, -1)
end

main()
